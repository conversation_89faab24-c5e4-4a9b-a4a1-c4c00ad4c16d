# FouFouFood Server Architecture

This project combines the **original Simulator** with a clean **Controller-Service-Repository** architecture for API
endpoints. This gives you the best of both worlds: the original simulation functionality plus modern REST API
capabilities.

## 📁 Folder Structure

```
Foufoufood-server/
├── src/
│   ├── controllers/     # HTTP request handlers (for API)
│   ├── services/        # Business logic (for API)
│   ├── repositories/    # Data access layer (for API)
│   ├── models/          # Data models/entities (shared)
│   ├── routes/          # Route definitions (for API)
│   └── container.js     # Dependency injection (simplified)
├── Simulator.js        # Original simulation logic
├── app.js              # Main application entry point
└── package.json
```

## 🏗️ Architecture Layers

### 1. **Models** (`src/models/`)

- **Purpose**: Define data structures and entities
- **Files**: `User.js`, `Restaurant.js`, `Order.js`, `DeliveryPartner.js`, `MenuItem.js`
- **Responsibility**: Data representation only, no business logic

### 2. **Repositories** (`src/repositories/`)

- **Purpose**: Data access and storage operations
- **Files**: `UserRepository.js`, `RestaurantRepository.js`, `OrderRepository.js`, `DeliveryPartnerRepository.js`
- **Responsibility**: CRUD operations, data queries, in-memory storage management

### 3. **Services** (`src/services/`)

- **Purpose**: Business logic and rules
- **Files**: `UserService.js`, `RestaurantService.js`, `OrderService.js`, `DeliveryPartnerService.js`,
  `SimulatorService.js`
- **Responsibility**: Validation, business rules, orchestration between repositories

### 4. **Controllers** (`src/controllers/`)

- **Purpose**: Handle HTTP requests and responses
- **Files**: `UserController.js`, `RestaurantController.js`, `OrderController.js`, `DeliveryPartnerController.js`
- **Responsibility**: Request validation, response formatting, HTTP status codes

### 5. **Routes** (`src/routes/`)

- **Purpose**: Define API endpoints and route handlers
- **Files**: `userRoutes.js`, `restaurantRoutes.js`, `orderRoutes.js`, `deliveryPartnerRoutes.js`, `index.js`
- **Responsibility**: URL mapping, middleware setup

## 🔗 Two Systems Working Together

### Original Simulator

- **`Simulator.js`** - Your original simulation with all the data and logic
- **Legacy endpoints** (`/users`, `/restaurants`, `/orders`, `/deliverypartners`) show simulator data
- **Simulation runs automatically** when server starts

### API System (C-S-R Architecture)

- **Separate data storage** for API operations
- **Clean REST endpoints** under `/api/` prefix
- **Container.js** manages dependencies (simplified - no complex injection needed!)

```javascript
Repository → Service → Controller → Routes(
for API only
)
```

## 🚀 Simple API Endpoints (Basic CRUD Only)

### Users

- `GET /api/users` - Get all users
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Restaurants

- `GET /api/restaurants` - Get all restaurants
- `POST /api/restaurants` - Create restaurant
- `PUT /api/restaurants/:id` - Update restaurant
- `DELETE /api/restaurants/:id` - Delete restaurant

### Orders

- `GET /api/orders` - Get all orders
- `POST /api/orders` - Create new order
- `PUT /api/orders/:id` - Update order
- `DELETE /api/orders/:id` - Delete order

### Delivery Partners

- `GET /api/delivery-partners` - Get all delivery partners
- `POST /api/delivery-partners` - Create delivery partner
- `PUT /api/delivery-partners/:id` - Update delivery partner
- `DELETE /api/delivery-partners/:id` - Delete delivery partner
