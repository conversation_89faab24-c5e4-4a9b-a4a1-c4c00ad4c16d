class UserController {
    constructor(userService) {
        this.userService = userService;
    }

    // GET all users (async)
    async getAllUsers(req, res) {
        try {
            const users = await this.userService.getAllUsers();
            res.status(200).json({
                success: true,
                data: users,
                message: 'Users retrieved successfully'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // POST create new user (async)
    async createUser(req, res) {
        try {
            const userData = req.body;
            const newUser = await this.userService.createUser(userData);
            res.status(201).json({
                success: true,
                data: newUser,
                message: 'User created successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('already exists') ? 409 : 400;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }

    // PUT update user (async)
    async updateUser(req, res) {
        try {
            const {id} = req.params;
            const userData = req.body;
            const updatedUser = await this.userService.updateUser(id, userData);
            res.status(200).json({
                success: true,
                data: updatedUser,
                message: 'User updated successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('not found') ? 404 :
                error.message.includes('already exists') ? 409 : 400;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }

    // DELETE user (async)
    async deleteUser(req, res) {
        try {
            const {id} = req.params;
            const deletedUser = await this.userService.deleteUser(id);
            res.status(200).json({
                success: true,
                data: deletedUser,
                message: 'User deleted successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('not found') ? 404 : 500;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }
}

module.exports = UserController;
