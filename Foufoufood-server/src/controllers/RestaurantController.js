class RestaurantController {
    constructor(restaurantService) {
        this.restaurantService = restaurantService;
    }

    // GET all restaurants (async)
    async getAllRestaurants(req, res) {
        try {
            const restaurants = await this.restaurantService.getAllRestaurants();
            res.status(200).json({
                success: true,
                data: restaurants,
                message: 'Restaurants retrieved successfully'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // POST create new restaurant (async)
    async createRestaurant(req, res) {
        try {
            const restaurantData = req.body;
            const newRestaurant = await this.restaurantService.createRestaurant(restaurantData);
            res.status(201).json({
                success: true,
                data: newRestaurant,
                message: 'Restaurant created successfully'
            });
        } catch (error) {
            res.status(400).json({
                success: false,
                message: error.message
            });
        }
    }

    // PUT update restaurant (async)
    async updateRestaurant(req, res) {
        try {
            const {id} = req.params;
            const restaurantData = req.body;
            const updatedRestaurant = await this.restaurantService.updateRestaurant(id, restaurantData);
            res.status(200).json({
                success: true,
                data: updatedRestaurant,
                message: 'Restaurant updated successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('not found') ? 404 : 400;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }

    // DELETE restaurant (async)
    async deleteRestaurant(req, res) {
        try {
            const {id} = req.params;
            const deletedRestaurant = await this.restaurantService.deleteRestaurant(id);
            res.status(200).json({
                success: true,
                data: deletedRestaurant,
                message: 'Restaurant deleted successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('not found') ? 404 : 500;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }
}

module.exports = RestaurantController;
