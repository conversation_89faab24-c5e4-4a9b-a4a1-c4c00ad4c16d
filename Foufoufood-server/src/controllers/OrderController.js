class OrderController {
    constructor(orderService) {
        this.orderService = orderService;
    }

    // GET all orders (async)
    async getAllOrders(req, res) {
        try {
            const orders = await this.orderService.getAllOrders();
            res.status(200).json({
                success: true,
                data: orders,
                message: 'Orders retrieved successfully'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // POST create new order (async)
    async createOrder(req, res) {
        try {
            const orderData = req.body;
            const newOrder = await this.orderService.createOrder(orderData);
            res.status(201).json({
                success: true,
                data: newOrder,
                message: 'Order created successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('not found') ? 404 : 400;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }

    // PUT update order (async)
    async updateOrder(req, res) {
        try {
            const {id} = req.params;
            const orderData = req.body;
            const updatedOrder = await this.orderService.updateOrder(id, orderData);
            res.status(200).json({
                success: true,
                data: updatedOrder,
                message: 'Order updated successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('not found') ? 404 : 400;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }

    // DELETE order (async)
    async deleteOrder(req, res) {
        try {
            const {id} = req.params;
            const deletedOrder = await this.orderService.deleteOrder(id);
            res.status(200).json({
                success: true,
                data: deletedOrder,
                message: 'Order deleted successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('not found') ? 404 : 500;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }
}

module.exports = OrderController;
