class DeliveryPartnerController {
    constructor(deliveryPartnerService) {
        this.deliveryPartnerService = deliveryPartnerService;
    }

    // GET all delivery partners (async)
    async getAllDeliveryPartners(req, res) {
        try {
            const partners = await this.deliveryPartnerService.getAllDeliveryPartners();
            res.status(200).json({
                success: true,
                data: partners,
                message: 'Delivery partners retrieved successfully'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // POST create new delivery partner (async)
    async createDeliveryPartner(req, res) {
        try {
            const partnerData = req.body;
            const newPartner = await this.deliveryPartnerService.createDeliveryPartner(partnerData);
            res.status(201).json({
                success: true,
                data: newPartner,
                message: 'Delivery partner created successfully'
            });
        } catch (error) {
            res.status(400).json({
                success: false,
                message: error.message
            });
        }
    }

    // PUT update delivery partner (async)
    async updateDeliveryPartner(req, res) {
        try {
            const {id} = req.params;
            const partnerData = req.body;
            const updatedPartner = await this.deliveryPartnerService.updateDeliveryPartner(id, partnerData);
            res.status(200).json({
                success: true,
                data: updatedPartner,
                message: 'Delivery partner updated successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('not found') ? 404 : 400;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }

    // DELETE delivery partner (async)
    async deleteDeliveryPartner(req, res) {
        try {
            const {id} = req.params;
            const deletedPartner = await this.deliveryPartnerService.deleteDeliveryPartner(id);
            res.status(200).json({
                success: true,
                data: deletedPartner,
                message: 'Delivery partner deleted successfully'
            });
        } catch (error) {
            const statusCode = error.message.includes('not found') ? 404 : 500;
            res.status(statusCode).json({
                success: false,
                message: error.message
            });
        }
    }
}

module.exports = DeliveryPartnerController;
