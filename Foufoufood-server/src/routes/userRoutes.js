const express = require('express');
const router = express.Router();

// This will be injected when the routes are set up
let userController;

// Set up the controller
const setController = (controller) => {
    userController = controller;
};

// Basic CRUD routes for users (async)
router.get('/', async (req, res) => await userController.getAllUsers(req, res));        // GET all users
router.post('/', async (req, res) => await userController.createUser(req, res));        // CREATE user
router.put('/:id', async (req, res) => await userController.updateUser(req, res));      // UPDATE user
router.delete('/:id', async (req, res) => await userController.deleteUser(req, res));   // DELETE user

module.exports = {router, setController};
