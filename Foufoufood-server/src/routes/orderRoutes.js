const express = require('express');
const router = express.Router();

// This will be injected when the routes are set up
let orderController;

// Set up the controller
const setController = (controller) => {
    orderController = controller;
};

// Basic CRUD routes for orders (async)
router.get('/', async (req, res) => await orderController.getAllOrders(req, res));        // GET all orders
router.post('/', async (req, res) => await orderController.createOrder(req, res));        // CREATE order
router.put('/:id', async (req, res) => await orderController.updateOrder(req, res));      // UPDATE order
router.delete('/:id', async (req, res) => await orderController.deleteOrder(req, res));   // DELETE order

module.exports = {router, setController};
