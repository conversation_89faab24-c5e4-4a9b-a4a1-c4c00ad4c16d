const express = require('express');
const router = express.Router();

// This will be injected when the routes are set up
let deliveryPartnerController;

// Set up the controller
const setController = (controller) => {
    deliveryPartnerController = controller;
};

// Basic CRUD routes for delivery partners (async)
router.get('/', async (req, res) => await deliveryPartnerController.getAllDeliveryPartners(req, res));        // GET all delivery partners
router.post('/', async (req, res) => await deliveryPartnerController.createDeliveryPartner(req, res));        // CREATE delivery partner
router.put('/:id', async (req, res) => await deliveryPartnerController.updateDeliveryPartner(req, res));      // UPDATE delivery partner
router.delete('/:id', async (req, res) => await deliveryPartnerController.deleteDeliveryPartner(req, res));   // DELETE delivery partner

module.exports = {router, setController};
