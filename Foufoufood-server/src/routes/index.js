const userRoutes = require('./userRoutes');
const restaurantRoutes = require('./restaurantRoutes');
const orderRoutes = require('./orderRoutes');
const deliveryPartnerRoutes = require('./deliveryPartnerRoutes');

// Main routes setup function
const setupRoutes = (app, controllers, simulator = null) => {
    // Set up controllers for each route module
    userRoutes.setController(controllers.userController);
    restaurantRoutes.setController(controllers.restaurantController);
    orderRoutes.setController(controllers.orderController);
    deliveryPartnerRoutes.setController(controllers.deliveryPartnerController);

    // Home route
    app.get('/', (req, res) => {
        res.json({
            success: true,
            message: 'Bienvenue sur FouFouFood API!',
            version: '1.0.0',
            endpoints: {
                users: '/api/users',
                restaurants: '/api/restaurants',
                orders: '/api/orders',
                deliveryPartners: '/api/delivery-partners'
            }
        });
    });

    // API routes
    app.use('/api/users', userRoutes.router);
    app.use('/api/restaurants', restaurantRoutes.router);
    app.use('/api/orders', orderRoutes.router);
    app.use('/api/delivery-partners', deliveryPartnerRoutes.router);

    // Legacy routes for backward compatibility - show simulator data if available
    app.get('/users', (req, res) => {
        if (simulator) {
            res.send(`<h1>Utilisateurs</h1><br> ${JSON.stringify(simulator.users)}`);
        } else {
            res.redirect('/api/users');
        }
    });

    app.get('/restaurants', (req, res) => {
        if (simulator) {
            res.send(`<h1>Restaurants</h1><br> ${JSON.stringify(simulator.restaurants)}`);
        } else {
            res.redirect('/api/restaurants');
        }
    });

    app.get('/orders', (req, res) => {
        if (simulator) {
            res.send(`<h1>Commandes</h1><br> ${JSON.stringify(simulator.orders)}`);
        } else {
            res.redirect('/api/orders');
        }
    });

    app.get('/deliverypartners', (req, res) => {
        if (simulator) {
            res.send(`<h1>Livreurs</h1><br> ${JSON.stringify(simulator.deliveryPartners)}`);
        } else {
            res.redirect('/api/delivery-partners');
        }
    });

    // 404 handler
    app.use('*', (req, res) => {
        res.status(404).json({
            success: false,
            message: 'Endpoint not found',
            availableEndpoints: {
                users: '/api/users',
                restaurants: '/api/restaurants',
                orders: '/api/orders',
                deliveryPartners: '/api/delivery-partners'
            }
        });
    });
};

module.exports = setupRoutes;
