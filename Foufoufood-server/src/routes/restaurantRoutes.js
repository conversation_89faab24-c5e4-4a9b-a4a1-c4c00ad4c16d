const express = require('express');
const router = express.Router();

// This will be injected when the routes are set up
let restaurantController;

// Set up the controller
const setController = (controller) => {
    restaurantController = controller;
};

// Basic CRUD routes for restaurants (async)
router.get('/', async (req, res) => await restaurantController.getAllRestaurants(req, res));        // GET all restaurants
router.post('/', async (req, res) => await restaurantController.createRestaurant(req, res));        // CREATE restaurant
router.put('/:id', async (req, res) => await restaurantController.updateRestaurant(req, res));      // UPDATE restaurant
router.delete('/:id', async (req, res) => await restaurantController.deleteRestaurant(req, res));   // DELETE restaurant

module.exports = {router, setController};
