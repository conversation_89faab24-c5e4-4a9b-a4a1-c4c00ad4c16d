class RestaurantService {
    constructor(restaurantRepository) {
        this.restaurantRepository = restaurantRepository;
    }

    // Get all restaurants (async)
    async getAllRestaurants() {
        return await this.restaurantRepository.findAll();
    }

    // Get restaurant by ID (async)
    async getRestaurantById(id) {
        const restaurant = await this.restaurantRepository.findById(id);
        if (!restaurant) {
            throw new Error(`Restaurant with ID ${id} not found`);
        }
        return restaurant;
    }

    // Create new restaurant (async)
    async createRestaurant(restaurantData) {
        // Validate required fields
        if (!restaurantData.name || !restaurantData.address) {
            throw new Error('Name and address are required');
        }

        // Generate ID if not provided
        if (!restaurantData.id) {
            restaurantData.id = await this.generateRestaurantId();
        }

        return this.restaurantRepository.create(restaurantData);
    }

    // Update restaurant (async)
    async updateRestaurant(id, restaurantData) {
        const existingRestaurant = await this.restaurantRepository.findById(id);
        if (!existingRestaurant) {
            throw new Error(`Restaurant with ID ${id} not found`);
        }

        return await this.restaurantRepository.update(id, restaurantData);
    }

    // Delete restaurant (async)
    async deleteRestaurant(id) {
        const restaurant = await this.restaurantRepository.findById(id);
        if (!restaurant) {
            throw new Error(`Restaurant with ID ${id} not found`);
        }

        return await this.restaurantRepository.delete(id);
    }

    // Generate unique restaurant ID (async)
    async generateRestaurantId() {
        const restaurants = await this.restaurantRepository.findAll();
        const maxId = restaurants.reduce((max, restaurant) => {
            const numId = parseInt(restaurant.id);
            return numId > max ? numId : max;
        }, 0);
        return (maxId + 1).toString();
    }
}

module.exports = RestaurantService;
