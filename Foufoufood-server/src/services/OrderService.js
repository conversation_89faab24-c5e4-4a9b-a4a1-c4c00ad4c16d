class OrderService {
    constructor(orderRepository, userService, restaurantService, deliveryPartnerService) {
        this.orderRepository = orderRepository;
        this.userService = userService;
        this.restaurantService = restaurantService;
        this.deliveryPartnerService = deliveryPartnerService;
    }

    // Get all orders (async)
    async getAllOrders() {
        return await this.orderRepository.findAll();
    }

    // Create new order (async)
    async createOrder(orderData) {
        // Validate required fields
        if (!orderData.userId || !orderData.restaurantId || !orderData.items || orderData.items.length === 0) {
            throw new Error('User ID, Restaurant ID, and items are required');
        }

        // Verify user exists
        await this.userService.getUserById(orderData.userId);

        // Verify restaurant exists
        await this.restaurantService.getRestaurantById(orderData.restaurantId);

        // Calculate total price if not provided
        if (!orderData.totalPrice) {
            orderData.totalPrice = this.calculateTotalPrice(orderData.items);
        }

        // Generate ID if not provided
        if (!orderData.id) {
            orderData.id = await this.generateOrderId();
        }

        // Set delivery address from user if not provided
        if (!orderData.deliveryAddress) {
            const user = await this.userService.getUserById(orderData.userId);
            orderData.deliveryAddress = user.address;
        }

        const newOrder = await this.orderRepository.create(orderData);

        // Add order to user's order list
        await this.userService.addOrderToUser(orderData.userId, newOrder);

        return newOrder;
    }

    // Update order (async)
    async updateOrder(id, orderData) {
        const existingOrder = await this.orderRepository.findById(id);
        if (!existingOrder) {
            throw new Error(`Order with ID ${id} not found`);
        }

        return await this.orderRepository.update(id, orderData);
    }

    // Delete order (async)
    async deleteOrder(id) {
        const order = await this.orderRepository.findById(id);
        if (!order) {
            throw new Error(`Order with ID ${id} not found`);
        }

        return await this.orderRepository.delete(id);
    }

    // Calculate total price for items
    calculateTotalPrice(items) {
        return items.reduce((total, item) => total + item.price, 0);
    }

    // Generate unique order ID (async)
    async generateOrderId() {
        const crypto = require('crypto');
        return crypto.randomUUID();
    }

}

module.exports = OrderService;
