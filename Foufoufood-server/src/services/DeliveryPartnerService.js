class DeliveryPartnerService {
    constructor(deliveryPartnerRepository) {
        this.deliveryPartnerRepository = deliveryPartnerRepository;
    }

    // Get all delivery partners (async)
    async getAllDeliveryPartners() {
        return await this.deliveryPartnerRepository.findAll();
    }

    // Create new delivery partner (async)
    async createDeliveryPartner(partnerData) {
        // Validate required fields
        if (!partnerData.name || !partnerData.vehicleType) {
            throw new Error('Name and vehicle type are required');
        }

        // Generate ID if not provided
        if (!partnerData.id) {
            partnerData.id = await this.generateDeliveryPartnerId();
        }

        // Set default location if not provided
        if (!partnerData.location) {
            partnerData.location = {lat: 45.5, lng: -73.6}; // Default Montreal coordinates
        }

        return this.deliveryPartnerRepository.create(partnerData);
    }

    // Update delivery partner (async)
    async updateDeliveryPartner(id, partnerData) {
        const existingPartner = await this.deliveryPartnerRepository.findById(id);
        if (!existingPartner) {
            throw new Error(`Delivery partner with ID ${id} not found`);
        }

        return await this.deliveryPartnerRepository.update(id, partnerData);
    }

    // Delete delivery partner (async)
    async deleteDeliveryPartner(id) {
        const partner = await this.deliveryPartnerRepository.findById(id);
        if (!partner) {
            throw new Error(`Delivery partner with ID ${id} not found`);
        }

        return await this.deliveryPartnerRepository.delete(id);
    }

    // Generate unique delivery partner ID (async)
    async generateDeliveryPartnerId() {
        const partners = await this.deliveryPartnerRepository.findAll();
        const maxId = partners.reduce((max, partner) => {
            const numId = parseInt(partner.id);
            return numId > max ? numId : max;
        }, 400);
        return (maxId + 1).toString();
    }

}

module.exports = DeliveryPartnerService;
