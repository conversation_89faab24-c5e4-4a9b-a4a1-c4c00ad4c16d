class UserService {
    constructor(userRepository) {
        this.userRepository = userRepository;
    }

    // Get all users (async)
    async getAllUsers() {
        return await this.userRepository.findAll();
    }

    // Get user by ID (async)
    async getUserById(id) {
        const user = await this.userRepository.findById(id);
        if (!user) {
            throw new Error(`User with ID ${id} not found`);
        }
        return user;
    }

    // Create new user (async)
    async createUser(userData) {
        // Validate required fields
        if (!userData.name || !userData.email) {
            throw new Error('Name and email are required');
        }

        // Check if email already exists
        const existingUser = await this.userRepository.findByEmail(userData.email);
        if (existingUser) {
            throw new Error('User with this email already exists');
        }

        // Generate ID if not provided
        if (!userData.id) {
            userData.id = await this.generateUserId();
        }

        return this.userRepository.create(userData);
    }

    // Update user (async)
    async updateUser(id, userData) {
        const existingUser = await this.userRepository.findById(id);
        if (!existingUser) {
            throw new Error(`User with ID ${id} not found`);
        }

        // If email is being updated, check for duplicates
        if (userData.email && userData.email !== existingUser.email) {
            const emailExists = await this.userRepository.findByEmail(userData.email);
            if (emailExists) {
                throw new Error('User with this email already exists');
            }
        }

        return await this.userRepository.update(id, userData);
    }

    // Delete user (async)
    async deleteUser(id) {
        const user = await this.userRepository.findById(id);
        if (!user) {
            throw new Error(`User with ID ${id} not found`);
        }

        return await this.userRepository.delete(id);
    }

    // Add order to user (async)
    async addOrderToUser(userId, order) {
        const user = await this.getUserById(userId);
        user.orders.push(order);
        return await this.userRepository.update(userId, {orders: user.orders});
    }

    // Generate unique user ID (async)
    async generateUserId() {
        const users = await this.userRepository.findAll();
        const maxId = users.reduce((max, user) => {
            const numId = parseInt(user.id);
            return numId > max ? numId : max;
        }, 300);
        return (maxId + 1).toString();
    }

}

module.exports = UserService;
