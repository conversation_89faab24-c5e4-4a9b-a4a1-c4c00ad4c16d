const Restaurant = require('../models/Restaurant');

class RestaurantRepository {
    constructor() {
        // In-memory storage - empty initially, will be populated by Simulator
        this.restaurants = [];
    }

    // Get all restaurants (async)
    async findAll() {
        return this.restaurants;
    }

    // Find restaurant by ID (async)
    async findById(id) {
        return this.restaurants.find(restaurant => restaurant.id === id);
    }

    // Create new restaurant (async)
    async create(restaurantData) {
        const newRestaurant = new Restaurant(
            restaurantData.id,
            restaurantData.name,
            restaurantData.address,
            restaurantData.cuisine,
            restaurantData.phone,
            restaurantData.openingHours,
            restaurantData.menu || [],
            restaurantData.rating || 0,
            restaurantData.reviews || []
        );
        this.restaurants.push(newRestaurant);
        return newRestaurant;
    }

    // Update restaurant (async)
    async update(id, restaurantData) {
        const restaurantIndex = this.restaurants.findIndex(restaurant => restaurant.id === id);
        if (restaurantIndex !== -1) {
            this.restaurants[restaurantIndex] = {...this.restaurants[restaurantIndex], ...restaurantData};
            return this.restaurants[restaurantIndex];
        }
        return null;
    }

    // Delete restaurant (async)
    async delete(id) {
        const restaurantIndex = this.restaurants.findIndex(restaurant => restaurant.id === id);
        if (restaurantIndex !== -1) {
            return this.restaurants.splice(restaurantIndex, 1)[0];
        }
        return null;
    }
}

module.exports = RestaurantRepository;
