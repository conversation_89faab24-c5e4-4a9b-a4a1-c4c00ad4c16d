const User = require('../models/User');

class UserRepository {
    constructor() {
        // In-memory storage - empty initially, will be populated by Simulator
        this.users = [];
    }

    // Get all users (async)
    async findAll() {
        return this.users;
    }

    // Find user by ID (async)
    async findById(id) {
        return this.users.find(user => user.id === id);
    }

    // Find user by email (async)
    async findByEmail(email) {
        return this.users.find(user => user.email === email);
    }

    // Create new user (async)
    async create(userData) {
        const newUser = new User(
            userData.id,
            userData.name,
            userData.email,
            userData.password,
            userData.phone,
            userData.address,
            userData.isAdmin || false,
            userData.orders || []
        );
        this.users.push(newUser);
        return newUser;
    }

    // Update user (async)
    async update(id, userData) {
        const userIndex = this.users.findIndex(user => user.id === id);
        if (userIndex !== -1) {
            this.users[userIndex] = {...this.users[userIndex], ...userData};
            return this.users[userIndex];
        }
        return null;
    }

    // Delete user (async)
    async delete(id) {
        const userIndex = this.users.findIndex(user => user.id === id);
        if (userIndex !== -1) {
            return this.users.splice(userIndex, 1)[0];
        }
        return null;
    }
}

module.exports = UserRepository;
