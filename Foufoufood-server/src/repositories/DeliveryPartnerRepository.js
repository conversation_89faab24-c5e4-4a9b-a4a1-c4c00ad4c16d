const DeliveryPartner = require('../models/DeliveryPartner');

class DeliveryPartnerRepository {
    constructor() {
        // In-memory storage - empty initially, will be populated by Simulator
        this.deliveryPartners = [];
    }

    // Get all delivery partners (async)
    async findAll() {
        return this.deliveryPartners;
    }

    // Find delivery partner by ID (async)
    async findById(id) {
        return this.deliveryPartners.find(partner => partner.id === id);
    }

    // Find available delivery partners (async)
    async findAvailable() {
        return this.deliveryPartners.filter(partner => partner.status === 'Disponible');
    }

    // Create new delivery partner (async)
    async create(partnerData) {
        const newPartner = new DeliveryPartner(
            partnerData.id,
            partnerData.name,
            partnerData.vehicleType,
            partnerData.location,
            partnerData.status || 'Disponible'
        );
        this.deliveryPartners.push(newPartner);
        return newPartner;
    }

    // Update delivery partner (async)
    async update(id, partnerData) {
        const partnerIndex = this.deliveryPartners.findIndex(partner => partner.id === id);
        if (partnerIndex !== -1) {
            this.deliveryPartners[partnerIndex] = {...this.deliveryPartners[partnerIndex], ...partnerData};
            return this.deliveryPartners[partnerIndex];
        }
        return null;
    }

    // Delete delivery partner (async)
    async delete(id) {
        const partnerIndex = this.deliveryPartners.findIndex(partner => partner.id === id);
        if (partnerIndex !== -1) {
            return this.deliveryPartners.splice(partnerIndex, 1)[0];
        }
        return null;
    }
}

module.exports = DeliveryPartnerRepository;
