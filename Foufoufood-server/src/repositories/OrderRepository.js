const Order = require('../models/Order');

class OrderRepository {
    constructor() {
        // In-memory storage for simplicity
        this.orders = [];
    }

    // Get all orders (async)
    async findAll() {
        return this.orders;
    }

    // Find order by ID (async)
    async findById(id) {
        return this.orders.find(order => order.id === id);
    }

    // Create new order (async)
    async create(orderData) {
        const newOrder = new Order(
            orderData.id,
            orderData.userId,
            orderData.restaurantId,
            orderData.items,
            orderData.totalPrice,
            orderData.status || 'En cours',
            orderData.deliveryAddress,
            orderData.deliveryPartner || null,
            orderData.createdAt || new Date(),
            orderData.updatedAt || new Date()
        );
        this.orders.push(newOrder);
        return newOrder;
    }

    // Update order (async)
    async update(id, orderData) {
        const orderIndex = this.orders.findIndex(order => order.id === id);
        if (orderIndex !== -1) {
            this.orders[orderIndex] = {
                ...this.orders[orderIndex],
                ...orderData,
                updatedAt: new Date()
            };
            return this.orders[orderIndex];
        }
        return null;
    }

    // Delete order (async)
    async delete(id) {
        const orderIndex = this.orders.findIndex(order => order.id === id);
        if (orderIndex !== -1) {
            return this.orders.splice(orderIndex, 1)[0];
        }
        return null;
    }
}

module.exports = OrderRepository;
