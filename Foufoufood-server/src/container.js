// Repositories
const UserRepository = require('./repositories/UserRepository');
const RestaurantRepository = require('./repositories/RestaurantRepository');
const OrderRepository = require('./repositories/OrderRepository');
const DeliveryPartnerRepository = require('./repositories/DeliveryPartnerRepository');

// Services
const UserService = require('./services/UserService');
const RestaurantService = require('./services/RestaurantService');
const OrderService = require('./services/OrderService');
const DeliveryPartnerService = require('./services/DeliveryPartnerService');

// Controllers
const UserController = require('./controllers/UserController');
const RestaurantController = require('./controllers/RestaurantController');
const OrderController = require('./controllers/OrderController');
const DeliveryPartnerController = require('./controllers/DeliveryPartnerController');

class Container {
    constructor() {
        this.instances = {};
        this.setupDependencies();
    }

    setupDependencies() {
        // Create repositories (data layer) - but keep them simple, no pre-loaded data
        this.instances.userRepository = new UserRepository();
        this.instances.restaurantRepository = new RestaurantRepository();
        this.instances.orderRepository = new OrderRepository();
        this.instances.deliveryPartnerRepository = new DeliveryPartnerRepository();

        // Create services (business logic layer)
        this.instances.userService = new UserService(this.instances.userRepository);
        this.instances.restaurantService = new RestaurantService(this.instances.restaurantRepository);
        this.instances.deliveryPartnerService = new DeliveryPartnerService(this.instances.deliveryPartnerRepository);

        // OrderService needs references to other services for validation
        this.instances.orderService = new OrderService(
            this.instances.orderRepository,
            this.instances.userService,
            this.instances.restaurantService,
            this.instances.deliveryPartnerService
        );

        // Create controllers (presentation layer)
        this.instances.userController = new UserController(this.instances.userService);
        this.instances.restaurantController = new RestaurantController(this.instances.restaurantService);
        this.instances.orderController = new OrderController(this.instances.orderService);
        this.instances.deliveryPartnerController = new DeliveryPartnerController(this.instances.deliveryPartnerService);
    }

    // Get a specific instance
    get(name) {
        if (!this.instances[name]) {
            throw new Error(`Service '${name}' not found in container`);
        }
        return this.instances[name];
    }

    // Get all controllers for route setup
    getControllers() {
        return {
            userController: this.instances.userController,
            restaurantController: this.instances.restaurantController,
            orderController: this.instances.orderController,
            deliveryPartnerController: this.instances.deliveryPartnerController
        };
    }
}

module.exports = Container;
