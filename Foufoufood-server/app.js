const express = require('express');
const Container = require('./src/container');
const setupRoutes = require('./src/routes');
const Simulator = require('./Simulator');

// Create Express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({extended: true}));

// Create dependency injection container for API endpoints
const container = new Container();

// Create and start original simulator
const simulator = new Simulator();
simulator.startSimulation();

// Setup API routes with controllers and pass simulator for legacy routes
setupRoutes(app, container.getControllers(), simulator);

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`FouFouFood Server listening on port ${PORT}`);
    console.log(`API Documentation available at http://localhost:${PORT}/`);
});

